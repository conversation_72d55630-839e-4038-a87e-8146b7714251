{"pins": [{"identity": "abseil-cpp-swiftpm", "kind": "remoteSourceControl", "location": "https://github.com/firebase/abseil-cpp-SwiftPM.git", "state": {"revision": "583de9bd60f66b40e78d08599cc92036c2e7e4e1", "version": "0.20220203.2"}}, {"identity": "boringssl-swiftpm", "kind": "remoteSourceControl", "location": "https://github.com/firebase/boringssl-SwiftPM.git", "state": {"revision": "dd3eda2b05a3f459fc3073695ad1b28659066eab", "version": "0.9.1"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "7e80c25b51c2ffa238879b07fbfc5baa54bb3050", "version": "9.6.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "c1cfde8067668027b23a42c29d11c246152fe046", "version": "9.6.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "5056b15c5acbb90cd214fe4d6138bdf5a740e5a8", "version": "9.2.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "22907832079d808e82f1182b21af58ef3880666f", "version": "7.8.0"}}, {"identity": "grpc-ios", "kind": "remoteSourceControl", "location": "https://github.com/grpc/grpc-ios.git", "state": {"revision": "8440b914756e0d26d4f4d054a1c1581daedfc5b6", "version": "1.44.3-grpc"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "d4289da23e978f37c344ea6a386e5546e2466294", "version": "2.1.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "0706abcc6b0bd9cedfbb015ba840e4a780b5159b", "version": "1.22.2"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "819d0a2173aff699fb8c364b6fb906f7cdb1a692", "version": "2.30909.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "3e4e743631e86c8c70dbc6efdc7beaa6e90fd3bb", "version": "2.1.1"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "88c7d15e1242fdb6ecbafbc7926426a19be1e98a", "version": "1.20.2"}}], "version": 2}