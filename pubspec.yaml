name: tiktok_flutter
description: A new Flutter project.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.0.0+1

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  video_player: ^2.9.5
  stacked_services: ^0.9.9
  get_it: ^7.7.0
  stacked: ^2.3.15
  cached_network_image: ^3.4.1
  firebase_core: ^1.24.0
  cloud_firestore: ^3.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  fonts:
    - family:  TikTokIcons
      fonts:
       - asset: assets/fonts/TikTokIcons.ttf

